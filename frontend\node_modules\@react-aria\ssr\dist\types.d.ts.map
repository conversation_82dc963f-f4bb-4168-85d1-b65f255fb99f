{"mappings": ";AAyCA;IACE,6BAA6B;IAC7B,QAAQ,EAAE,SAAS,CAAA;CACpB;AAoCD;;;GAGG;AACH,4BAA4B,KAAK,EAAE,gBAAgB,GAAG,IAAI,OAAO,CAShE;AAiED,oCAA4B,SAAS,CAAC,EAAE,MAAM,GAAG,MAAM,CAKtD;AAGD,eAAe;AACf,OAAO,MAAM,uCAA6F,CAAC;AAgB3G;;;;GAIG;AACH,4BAA4B,OAAO,CAQlC", "sources": ["packages/@react-aria/ssr/src/packages/@react-aria/ssr/src/SSRProvider.tsx", "packages/@react-aria/ssr/src/packages/@react-aria/ssr/src/index.ts", "packages/@react-aria/ssr/src/index.ts"], "sourcesContent": [null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nexport {SSRProvider, useSSRSafeId, useIsSSR} from './SSRProvider';\nexport type {SSRProviderProps} from './SSRProvider';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}