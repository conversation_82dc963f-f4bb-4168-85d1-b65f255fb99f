{"name": "@tanstack/react-virtual", "version": "3.13.12", "description": "Headless UI for virtualizing scrollable elements in React", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/virtual.git", "directory": "packages/react-virtual"}, "homepage": "https://tanstack.com/virtual", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "vue", "solid", "virtual", "virtual-core", "datagrid"], "type": "module", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "dependencies": {"@tanstack/virtual-core": "3.13.12"}, "devDependencies": {"@testing-library/react": "^16.3.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.5.2", "react": "^18.3.1", "react-dom": "^18.3.1", "resize-observer-polyfill": "^1.5.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "scripts": {"clean": "premove ./dist ./coverage", "test:eslint": "eslint ./src", "test:types": "tsc", "test:lib": "vitest", "test:lib:dev": "pnpm run test:lib --watch", "test:build": "publint --strict", "build": "vite build", "test:e2e": "playwright test"}}