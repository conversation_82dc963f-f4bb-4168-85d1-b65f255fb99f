{"mappings": "AAeA,yCAAyC,IAAI,CAE5C;AAED,mCAAmC,OAAO,CAEzC;AAED,mCAAmC,IAAI,CAEtC;AAED,6BAA6B,OAAO,CAEnC", "sources": ["packages/@react-stately/flags/src/packages/@react-stately/flags/src/index.ts", "packages/@react-stately/flags/src/index.ts"], "sourcesContent": [null, "/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet _tableNestedRows = false;\nlet _shadowDOM = false;\n\nexport function enableTableNestedRows(): void {\n  _tableNestedRows = true;\n}\n\nexport function tableNestedRows(): boolean {\n  return _tableNestedRows;\n}\n\nexport function enableShadowDOM(): void {\n  _shadowDOM = true;\n}\n\nexport function shadowDOM(): boolean {\n  return _shadowDOM;\n}\n"], "names": [], "version": 3, "file": "types.d.ts.map"}